"""
Core agent loop for AI interactions.

Handles conversation management, tool execution, and streaming responses
with support for multiple AI providers and advanced features.
"""

import asyncio
import json
import platform
from typing import Dict, List, Optional, Any, AsyncIterator, Callable
from datetime import datetime
import uuid

from ai_terminal.providers.base import (
    BaseProvider,
    Message,
    MessageRole,
    StreamChunk,
    CompletionResponse,
    ProviderError,
)
from ai_terminal.agents.context import ConversationContext
from ai_terminal.agents.tools import ToolReg<PERSON><PERSON>, ToolResult
from ai_terminal.security.approval import ApprovalManager
from ai_terminal.utils.logger import get_logger, log_performance
from ai_terminal.config.settings import get_settings

logger = get_logger(__name__)


class SystemPromptGenerator:
    """Generates sophisticated, context-aware system prompts for AI interactions."""

    def __init__(
        self,
        provider: BaseProvider,
        tool_registry: ToolRegistry,
        approval_manager: Optional[ApprovalManager] = None,
        settings: Optional[Any] = None
    ):
        """Initialize the system prompt generator.

        Args:
            provider: AI provider instance
            tool_registry: Tool registry for available tools
            approval_manager: Approval manager for security context
            settings: Application settings
        """
        self.provider = provider
        self.tool_registry = tool_registry
        self.approval_manager = approval_manager
        self.settings = settings or get_settings()

    def generate_system_prompt(self, user_system_message: Optional[str] = None) -> str:
        """Generate a comprehensive system prompt.

        Args:
            user_system_message: Optional user-provided system message

        Returns:
            Complete system prompt string
        """
        prompt_sections = []

        # Core identity and role
        prompt_sections.append(self._generate_identity_section())

        # Environment and capabilities
        prompt_sections.append(self._generate_environment_section())

        # Tool system instructions
        if self.tool_registry and self.tool_registry.tools:
            prompt_sections.append(self._generate_tools_section())

        # Security and approval guidelines
        if self.approval_manager:
            prompt_sections.append(self._generate_security_section())

        # Response formatting guidelines
        prompt_sections.append(self._generate_formatting_section())

        # Error handling instructions
        prompt_sections.append(self._generate_error_handling_section())

        # Provider-specific optimizations
        prompt_sections.append(self._generate_provider_specific_section())

        # User's custom system message (if provided)
        if user_system_message:
            prompt_sections.append(f"\n## Additional Instructions\n{user_system_message}")

        return "\n\n".join(prompt_sections)

    def _generate_identity_section(self) -> str:
        """Generate the AI identity and role section."""
        return """# AI Terminal Assistant

You are an advanced AI assistant integrated into AI Terminal, a sophisticated command-line interface tool. Your primary role is to help users with various tasks through intelligent conversation and tool execution.

## Core Capabilities
- Execute system commands and file operations securely
- Analyze code, files, and system information
- Provide technical assistance and problem-solving
- Maintain conversation context and session history
- Adapt responses based on user preferences and system capabilities

## Interaction Principles
- Be helpful, accurate, and efficient in your responses
- Always prioritize user safety and system security
- Provide clear explanations for your actions and recommendations
- Ask for clarification when requests are ambiguous
- Respect user privacy and data security"""

    def _generate_environment_section(self) -> str:
        """Generate environment and system context section."""
        system_info = platform.system()
        python_version = platform.python_version()

        capabilities = self.provider.capabilities
        provider_features = []

        if capabilities.supports_streaming:
            provider_features.append("streaming responses")
        if capabilities.supports_functions:
            provider_features.append("function calling")
        if capabilities.supports_tools:
            provider_features.append("tool execution")
        if capabilities.supports_vision:
            provider_features.append("image analysis")

        features_text = ", ".join(provider_features) if provider_features else "basic text generation"

        return f"""## Environment Context

**System Information:**
- Operating System: {system_info}
- Python Version: {python_version}
- AI Provider: {self.provider.name}
- Provider Capabilities: {features_text}
- Context Length: {capabilities.max_context_length:,} tokens
- Max Output: {capabilities.max_output_tokens:,} tokens

**AI Terminal Features:**
- Secure command execution with approval system
- File operations with safety checks
- Session persistence and conversation history
- Multi-provider AI support
- Rich terminal interface with syntax highlighting"""

    def _generate_tools_section(self) -> str:
        """Generate tool system instructions section."""
        tools_info = []

        for tool_name, tool_def in self.tool_registry.tools.items():
            # Extract parameter info
            params = tool_def.parameters.get("properties", {})
            required_params = tool_def.parameters.get("required", [])

            param_descriptions = []
            for param_name, param_info in params.items():
                param_type = param_info.get("type", "string")
                param_desc = param_info.get("description", "")
                required_marker = " (required)" if param_name in required_params else " (optional)"
                param_descriptions.append(f"  - {param_name} ({param_type}){required_marker}: {param_desc}")

            params_text = "\n".join(param_descriptions) if param_descriptions else "  - No parameters required"

            tools_info.append(f"""**{tool_name}**
- Description: {tool_def.description}
- Parameters:
{params_text}""")

        tools_list = "\n\n".join(tools_info)

        return f"""## Available Tools

You have access to the following tools for executing tasks. Always use the appropriate tool when the user's request requires system interaction:

{tools_list}

### Tool Usage Guidelines
1. **Always explain** what you're going to do before using a tool
2. **Use tools judiciously** - only when necessary to fulfill the user's request
3. **Handle errors gracefully** - if a tool fails, explain what happened and suggest alternatives
4. **Respect security policies** - some operations may require user approval
5. **Provide context** - explain the results and their relevance to the user's request

### Tool Execution Flow
- Tool calls are automatically executed by the system
- Results are returned and integrated into the conversation
- Security-sensitive operations may prompt for user approval
- Failed operations will return error information for troubleshooting"""

    def _generate_security_section(self) -> str:
        """Generate security and approval guidelines section."""
        security_enabled = self.settings.security.sandbox_enabled
        approval_commands = self.settings.security.approval_required

        approval_list = ", ".join(approval_commands) if approval_commands else "none configured"

        return f"""## Security and Approval System

**Security Status:** {'Enabled' if security_enabled else 'Disabled'}

### Security Guidelines
1. **Command Execution**: All system commands are subject to security review
2. **File Operations**: File access is restricted to safe locations and operations
3. **Approval Required**: Certain operations require explicit user approval
4. **Sandboxing**: Commands may be executed in a restricted environment

### Commands Requiring Approval
The following commands typically require user approval: {approval_list}

### Security Best Practices
- **Explain risks** when suggesting potentially dangerous operations
- **Offer alternatives** for high-risk commands
- **Respect denials** if the user or system blocks an operation
- **Use least privilege** - request only the minimum permissions needed
- **Validate inputs** before executing commands with user-provided data

### When Operations Are Blocked
If a tool execution is denied:
1. Explain why the operation was blocked
2. Suggest safer alternatives if available
3. Ask the user if they want to modify the approach
4. Provide manual instructions if automated execution isn't possible"""

    def _generate_formatting_section(self) -> str:
        """Generate response formatting guidelines section."""
        return """## Response Formatting Guidelines

### General Formatting
- Use **clear, concise language** appropriate for technical users
- **Structure responses** with headers, lists, and code blocks for readability
- **Highlight important information** using markdown formatting
- **Provide examples** when explaining complex concepts or commands

### Code and Command Formatting
- Use `inline code` formatting for commands, file names, and short code snippets
- Use code blocks with language specification for longer code examples:
  ```bash
  # Example command
  ls -la /home/<USER>
  ```
- **Always explain** what commands do before or after showing them
- **Include expected output** when helpful for understanding

### Tool Usage Communication
- **Announce tool usage**: "I'll check the system information for you..."
- **Explain tool results**: "The command completed successfully and shows..."
- **Handle errors transparently**: "The operation failed because..."

### Progress and Status Updates
- For long-running operations, provide status updates
- Explain what you're doing and why
- Set appropriate expectations for response times"""

    def _generate_error_handling_section(self) -> str:
        """Generate error handling instructions section."""
        return """## Error Handling and Recovery

### When Tools Fail
1. **Acknowledge the failure** clearly and immediately
2. **Explain the likely cause** based on the error message
3. **Suggest specific solutions** or alternative approaches
4. **Offer to try again** with modified parameters if appropriate

### Common Error Scenarios
- **Permission denied**: Explain the security restriction and suggest alternatives
- **File not found**: Verify the path and suggest corrections
- **Command not found**: Check if the tool is installed or suggest alternatives
- **Network issues**: Explain connectivity problems and retry options
- **Timeout errors**: Suggest breaking down large operations

### Troubleshooting Approach
1. **Gather information** about the error context
2. **Analyze the root cause** systematically
3. **Provide step-by-step solutions** when possible
4. **Escalate to manual intervention** when automated solutions aren't available

### User Communication During Errors
- Be honest about limitations and failures
- Provide actionable next steps
- Ask clarifying questions when the error is ambiguous
- Offer to help with manual alternatives"""

    def _generate_provider_specific_section(self) -> str:
        """Generate provider-specific optimization instructions."""
        provider_name = self.provider.name.lower()
        capabilities = self.provider.capabilities

        # Provider-specific optimizations
        if provider_name in ["openai", "deepseek"]:
            optimization_tips = """
- Leverage function calling capabilities for complex tool interactions
- Use structured responses when appropriate
- Take advantage of large context windows for comprehensive analysis"""
        elif provider_name == "anthropic":
            optimization_tips = """
- Utilize Claude's strong reasoning capabilities for complex problem-solving
- Leverage the large context window for document analysis
- Use structured thinking for multi-step processes"""
        elif provider_name == "ollama":
            optimization_tips = """
- Be mindful of local processing limitations
- Optimize for efficiency and conciseness
- Consider that function calling may be limited"""
        else:
            optimization_tips = """
- Adapt responses to provider capabilities
- Use available features efficiently
- Provide clear, structured responses"""

        return f"""## Provider-Specific Optimizations

**Current Provider:** {self.provider.name}
**Capabilities:** Function calling: {capabilities.supports_functions}, Streaming: {capabilities.supports_streaming}, Vision: {capabilities.supports_vision}

### Optimization Guidelines
{optimization_tips}

### Context Management
- **Context Length:** {capabilities.max_context_length:,} tokens available
- **Output Limit:** {capabilities.max_output_tokens:,} tokens maximum
- **Memory Management:** Prioritize recent and relevant information
- **Efficiency:** Balance detail with conciseness based on context limits

### Response Strategy
- Adapt complexity to provider capabilities
- Use available features (streaming, functions, etc.) effectively
- Optimize for the specific strengths of {self.provider.name}"""


class AgentLoop:
    """Core agent loop for AI interactions."""

    def __init__(
        self,
        provider: BaseProvider,
        tool_registry: Optional[ToolRegistry] = None,
        approval_manager: Optional[ApprovalManager] = None,
        session_manager: Optional[Any] = None,  # Use Any to avoid circular import
    ):
        """Initialize the agent loop.
        
        Args:
            provider: AI provider instance
            tool_registry: Tool registry for function calling
            approval_manager: Approval manager for security
            session_manager: Session manager for persistence
        """
        self.provider = provider
        self.tool_registry = tool_registry or ToolRegistry()
        self.approval_manager = approval_manager
        self.session_manager = session_manager
        self.settings = get_settings()

        # Initialize system prompt generator
        self.system_prompt_generator = SystemPromptGenerator(
            provider=self.provider,
            tool_registry=self.tool_registry,
            approval_manager=self.approval_manager,
            settings=self.settings
        )

        # Current conversation context
        self.context: Optional[ConversationContext] = None
        
        # Event callbacks
        self.on_message_start: Optional[Callable[[Message], None]] = None
        self.on_message_chunk: Optional[Callable[[str], None]] = None
        self.on_message_complete: Optional[Callable[[Message], None]] = None
        self.on_tool_call: Optional[Callable[[str, Dict[str, Any]], None]] = None
        self.on_tool_result: Optional[Callable[[str, ToolResult], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
    
    async def start_conversation(
        self,
        system_message: Optional[str] = None,
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        session_id: Optional[str] = None,
    ) -> ConversationContext:
        """Start a new conversation or resume an existing one.
        
        Args:
            system_message: System message to set context
            model: Model to use for this conversation
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            session_id: Existing session ID to resume
            
        Returns:
            ConversationContext for the session
        """
        if session_id and self.session_manager:
            # Try to resume existing session
            try:
                self.context = await self.session_manager.load_session(session_id)
                logger.info(f"Resumed conversation session: {session_id}")
            except Exception as e:
                logger.warning(f"Failed to resume session {session_id}: {e}")
                self.context = None
        
        if not self.context:
            # Generate enhanced system prompt
            try:
                enhanced_system_message = self.system_prompt_generator.generate_system_prompt(
                    user_system_message=system_message
                )
                logger.debug(f"Generated enhanced system prompt ({len(enhanced_system_message)} chars)")
            except Exception as e:
                logger.warning(f"Failed to generate enhanced system prompt: {e}")
                enhanced_system_message = system_message or "You are a helpful AI assistant."

            # Create new conversation
            self.context = ConversationContext(
                session_id=session_id or str(uuid.uuid4()),
                provider=self.provider,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                system_message=enhanced_system_message,
            )
            logger.info(f"Started new conversation session: {self.context.session_id}")

        return self.context

    async def update_system_prompt(self, user_system_message: Optional[str] = None) -> None:
        """Update the system prompt for the current conversation.

        Args:
            user_system_message: Optional user-provided system message to include
        """
        if not self.context:
            raise ValueError("No active conversation. Call start_conversation() first.")

        try:
            enhanced_system_message = self.system_prompt_generator.generate_system_prompt(
                user_system_message=user_system_message
            )

            # Update the context's system message
            self.context.system_message = enhanced_system_message
            self.context.updated_at = datetime.now()

            # Save session if manager is available
            if self.session_manager:
                await self.session_manager.save_session(self.context)

            logger.info(f"Updated system prompt for session: {self.context.session_id}")

        except Exception as e:
            logger.error(f"Failed to update system prompt: {e}")
            raise

    async def regenerate_system_prompt(self) -> str:
        """Regenerate and return the current system prompt without updating the conversation.

        Returns:
            The generated system prompt string
        """
        try:
            current_user_message = None
            if self.context and self.context.system_message:
                # Try to extract user portion from existing system message
                # This is a simple heuristic - in practice, you might want to store this separately
                if "## Additional Instructions" in self.context.system_message:
                    parts = self.context.system_message.split("## Additional Instructions")
                    if len(parts) > 1:
                        current_user_message = parts[1].strip()

            return self.system_prompt_generator.generate_system_prompt(
                user_system_message=current_user_message
            )
        except Exception as e:
            logger.error(f"Failed to regenerate system prompt: {e}")
            return "You are a helpful AI assistant."

    async def send_message(
        self,
        content: str,
        role: MessageRole = MessageRole.USER,
        stream: bool = True,
        files: Optional[List[str]] = None,
    ) -> AsyncIterator[StreamChunk]:
        """Send a message and get AI response.
        
        Args:
            content: Message content
            role: Message role
            stream: Whether to stream the response
            files: Optional list of file paths to include
            
        Yields:
            StreamChunk objects with response content
        """
        if not self.context:
            raise ValueError("No active conversation. Call start_conversation() first.")
        
        # Process file attachments
        if files:
            content = await self._process_file_attachments(content, files)
        
        # Add user message to context
        user_message = Message(role=role, content=content)
        self.context.add_message(user_message)
        
        if self.on_message_start:
            self.on_message_start(user_message)
        
        # Save session if manager is available
        if self.session_manager:
            await self.session_manager.save_session(self.context)
        
        # Get AI response
        try:
            if stream:
                async for chunk in self._stream_response():
                    yield chunk
            else:
                response = await self._complete_response()
                yield StreamChunk(
                    content=response.content,
                    is_complete=True,
                    metadata=response.metadata,
                )
        except Exception as e:
            logger.error(f"Failed to get AI response: {e}", exc_info=True)
            if self.on_error:
                self.on_error(e)
            raise
    
    async def _stream_response(self) -> AsyncIterator[StreamChunk]:
        """Stream AI response with tool calling support."""
        messages = self.context.get_messages_for_api()
        tools = self.tool_registry.get_tool_definitions() if self.tool_registry else None
        
        with log_performance(logger, "stream_completion", model=self.context.model):
            accumulated_content = ""
            accumulated_tool_calls = []
            
            async for chunk in self.provider.stream_complete(
                messages=messages,
                model=self.context.model,
                temperature=self.context.temperature,
                max_tokens=self.context.max_tokens,
                tools=tools,
            ):
                # Handle content chunks
                if chunk.content:
                    accumulated_content += chunk.content
                    if self.on_message_chunk:
                        self.on_message_chunk(chunk.content)
                    yield chunk
                
                # Handle tool calls
                if chunk.tool_calls:
                    accumulated_tool_calls.extend(chunk.tool_calls)
                
                # Handle completion
                if chunk.is_complete:
                    # Create assistant message
                    assistant_message = Message(
                        role=MessageRole.ASSISTANT,
                        content=accumulated_content,
                        tool_calls=accumulated_tool_calls if accumulated_tool_calls else None,
                    )
                    self.context.add_message(assistant_message)
                    
                    if self.on_message_complete:
                        self.on_message_complete(assistant_message)
                    
                    # Execute tool calls if present
                    if accumulated_tool_calls:
                        async for tool_chunk in self._execute_tool_calls(accumulated_tool_calls):
                            yield tool_chunk
                    
                    # Save session
                    if self.session_manager:
                        await self.session_manager.save_session(self.context)
                    
                    yield chunk
                    break
    
    async def _complete_response(self) -> CompletionResponse:
        """Get complete AI response (non-streaming)."""
        messages = self.context.get_messages_for_api()
        tools = self.tool_registry.get_tool_definitions() if self.tool_registry else None
        
        with log_performance(logger, "completion", model=self.context.model):
            response = await self.provider.complete(
                messages=messages,
                model=self.context.model,
                temperature=self.context.temperature,
                max_tokens=self.context.max_tokens,
                tools=tools,
            )
        
        # Add assistant message to context
        assistant_message = Message(
            role=MessageRole.ASSISTANT,
            content=response.content,
            tool_calls=response.tool_calls,
        )
        self.context.add_message(assistant_message)
        
        if self.on_message_complete:
            self.on_message_complete(assistant_message)
        
        # Execute tool calls if present
        if response.tool_calls:
            await self._execute_tool_calls_sync(response.tool_calls)
        
        # Save session
        if self.session_manager:
            await self.session_manager.save_session(self.context)
        
        return response
    
    async def _execute_tool_calls(self, tool_calls: List[Dict[str, Any]]) -> AsyncIterator[StreamChunk]:
        """Execute tool calls and yield results."""
        for tool_call in tool_calls:
            tool_name = tool_call["function"]["name"]
            tool_args = json.loads(tool_call["function"]["arguments"])
            tool_id = tool_call.get("id", str(uuid.uuid4()))

            if self.on_tool_call:
                self.on_tool_call(tool_name, tool_args)

            # Check approval if required
            if self.approval_manager:
                approved = await self.approval_manager.request_approval(
                    tool_name, tool_args, self.context
                )
                if not approved:
                    result = ToolResult(
                        success=False,
                        content="Tool execution denied by approval policy",
                        metadata={"tool": tool_name, "reason": "approval_denied"}
                    )

                    # Add tool result message with proper tool_call_id
                    tool_message = Message(
                        role=MessageRole.TOOL,
                        content=result.content,
                        name=tool_name,
                        tool_call_id=tool_id,
                    )
                    self.context.add_message(tool_message)

                    yield StreamChunk(
                        content=f"\n[Tool execution denied: {tool_name}]\n",
                        is_complete=False,
                        metadata={"tool_result": result.dict()}
                    )
                    continue

            # Execute tool
            try:
                result = await self.tool_registry.execute_tool(tool_name, tool_args)

                if self.on_tool_result:
                    self.on_tool_result(tool_name, result)

                # Add tool result message with proper tool_call_id
                tool_message = Message(
                    role=MessageRole.TOOL,
                    content=result.content,
                    name=tool_name,
                    tool_call_id=tool_id,
                )
                self.context.add_message(tool_message)

                # Yield tool result
                yield StreamChunk(
                    content=f"\n[Tool: {tool_name}]\n{result.content}\n",
                    is_complete=False,
                    metadata={"tool_result": result.dict()}
                )

            except Exception as e:
                logger.error(f"Tool execution failed: {tool_name}: {e}", exc_info=True)
                error_result = ToolResult(
                    success=False,
                    content=f"Tool execution failed: {e}",
                    metadata={"tool": tool_name, "error": str(e)}
                )

                # Add error result message with proper tool_call_id
                tool_message = Message(
                    role=MessageRole.TOOL,
                    content=error_result.content,
                    name=tool_name,
                    tool_call_id=tool_id,
                )
                self.context.add_message(tool_message)

                yield StreamChunk(
                    content=f"\n[Tool error: {tool_name}] {e}\n",
                    is_complete=False,
                    metadata={"tool_result": error_result.dict()}
                )
    
    async def _execute_tool_calls_sync(self, tool_calls: List[Dict[str, Any]]) -> None:
        """Execute tool calls synchronously (for non-streaming mode)."""
        async for _ in self._execute_tool_calls(tool_calls):
            pass  # Just execute, don't yield
    
    async def _process_file_attachments(self, content: str, files: List[str]) -> str:
        """Process file attachments and include them in the message."""
        # This would integrate with the file operations utility
        # For now, just mention the files
        file_list = ", ".join(files)
        return f"{content}\n\nAttached files: {file_list}"
    
    async def clear_conversation(self) -> None:
        """Clear the current conversation."""
        if self.context:
            self.context.messages.clear()
            self.context.updated_at = datetime.now()
            
            if self.session_manager:
                await self.session_manager.save_session(self.context)
    
    async def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation."""
        if not self.context or not self.context.messages:
            return "No conversation history."
        
        message_count = len(self.context.messages)
        user_messages = len([m for m in self.context.messages if m.role == MessageRole.USER])
        assistant_messages = len([m for m in self.context.messages if m.role == MessageRole.ASSISTANT])
        
        return (
            f"Session: {self.context.session_id}\n"
            f"Messages: {message_count} total ({user_messages} user, {assistant_messages} assistant)\n"
            f"Model: {self.context.model or 'default'}\n"
            f"Started: {self.context.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Updated: {self.context.updated_at.strftime('%Y-%m-%d %H:%M:%S')}"
        )
